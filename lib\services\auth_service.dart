// Commented out for now - using mock auth instead
/*
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import 'supabase_service.dart';

class AuthService {
  static final SupabaseClient _client = SupabaseService.client;

  // Auth state stream
  static Stream<AuthState> get authStateChanges => _client.auth.onAuthStateChange;

  // Current user
  static User? get currentUser => _client.auth.currentUser;
  static String? get currentUserId => currentUser?.id;
  static bool get isAuthenticated => currentUser != null;

  // Sign up with email
  static Future<AuthResult> signUpWithEmail({
    required String email,
    required String password,
    required String fullName,
    required String role,
    String? phone,
  }) async {
    try {
      final response = await SupabaseService.signUpWithEmail(
        email: email,
        password: password,
        fullName: fullName,
        role: role,
        phone: phone,
      );

      if (response.user != null) {
        return AuthResult.success(
          user: response.user!,
          message: 'تم إنشاء الحساب بنجاح. يرجى التحقق من بريدك الإلكتروني.',
        );
      } else {
        return AuthResult.failure(
          message: 'فشل في إنشاء الحساب. يرجى المحاولة مرة أخرى.',
        );
      }
    } on AuthException catch (e) {
      return AuthResult.failure(message: _getArabicErrorMessage(e.message));
    } catch (e) {
      return AuthResult.failure(message: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.');
    }
  }

  // Sign in with email
  static Future<AuthResult> signInWithEmail({
    required String email,
    required String password,
  }) async {
    try {
      final response = await SupabaseService.signInWithEmail(
        email: email,
        password: password,
      );

      if (response.user != null) {
        return AuthResult.success(
          user: response.user!,
          message: 'تم تسجيل الدخول بنجاح.',
        );
      } else {
        return AuthResult.failure(
          message: 'فشل في تسجيل الدخول. يرجى التحقق من بياناتك.',
        );
      }
    } on AuthException catch (e) {
      return AuthResult.failure(message: _getArabicErrorMessage(e.message));
    } catch (e) {
      return AuthResult.failure(message: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.');
    }
  }

  // Sign out
  static Future<void> signOut() async {
    await SupabaseService.signOut();
  }

  // Reset password
  static Future<AuthResult> resetPassword(String email) async {
    try {
      await SupabaseService.resetPassword(email);
      return AuthResult.success(
        message: 'تم إرسال رابط إعادة تعيين كلمة المرور إلى بريدك الإلكتروني.',
      );
    } on AuthException catch (e) {
      return AuthResult.failure(message: _getArabicErrorMessage(e.message));
    } catch (e) {
      return AuthResult.failure(message: 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.');
    }
  }

  // Get current user profile
  static Future<UserModel?> getCurrentUserProfile() async {
    return await SupabaseService.getCurrentUserProfile();
  }

  // Update user profile
  static Future<bool> updateUserProfile(UserModel user) async {
    try {
      await SupabaseService.updateUserProfile(user);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Validate email format
  static bool isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  // Validate password strength
  static bool isValidPassword(String password) {
    return password.length >= 6;
  }

  // Validate phone number (Moroccan format)
  static bool isValidMoroccanPhone(String phone) {
    // Remove spaces and special characters
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');
    
    // Check for Moroccan phone patterns
    return RegExp(r'^(\+212|0)(6|7)[0-9]{8}$').hasMatch(cleanPhone);
  }

  // Convert error messages to Arabic
  static String _getArabicErrorMessage(String error) {
    final errorMap = {
      'Invalid login credentials': 'بيانات تسجيل الدخول غير صحيحة',
      'Email not confirmed': 'لم يتم تأكيد البريد الإلكتروني',
      'User already registered': 'المستخدم مسجل مسبقاً',
      'Password should be at least 6 characters': 'كلمة المرور يجب أن تكون 6 أحرف على الأقل',
      'Invalid email': 'البريد الإلكتروني غير صحيح',
      'Email rate limit exceeded': 'تم تجاوز الحد المسموح لإرسال الرسائل',
      'Signup disabled': 'التسجيل معطل حالياً',
      'Too many requests': 'عدد كبير من الطلبات. يرجى المحاولة لاحقاً',
      'Network error': 'خطأ في الشبكة. يرجى التحقق من اتصالك بالإنترنت',
    };

    return errorMap[error] ?? 'حدث خطأ غير متوقع. يرجى المحاولة مرة أخرى.';
  }
}

class AuthResult {
  final bool isSuccess;
  final User? user;
  final String message;

  AuthResult._({
    required this.isSuccess,
    this.user,
    required this.message,
  });

  factory AuthResult.success({User? user, required String message}) {
    return AuthResult._(
      isSuccess: true,
      user: user,
      message: message,
    );
  }

  factory AuthResult.failure({required String message}) {
    return AuthResult._(
      isSuccess: false,
      message: message,
    );
  }
}
*/

// Placeholder class for now
class AuthService {
  static bool get isAuthenticated => false;
}
