import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import '../pages/splash_page.dart';
import '../pages/auth/login_page.dart';
import '../pages/auth/register_page.dart';
import '../pages/auth/forgot_password_page.dart';
import '../pages/home/<USER>';
import '../pages/trip/trip_details_page.dart';
import '../pages/trip/create_trip_page.dart';
import '../services/auth_service.dart';

class NavigationService {
  static final GlobalKey<NavigatorState> navigatorKey =
      GlobalKey<NavigatorState>();

  static final GoRouter router = GoRouter(
    navigatorKey: navigatorKey,
    initialLocation: '/splash',
    redirect: (context, state) {
      final isAuthenticated = AuthService.isAuthenticated;
      final isOnAuthPage = state.matchedLocation.startsWith('/auth');
      final isOnSplash = state.matchedLocation == '/splash';

      // Allow splash page
      if (isOnSplash) return null;

      // Redirect to login if not authenticated and not on auth page
      if (!isAuthenticated && !isOnAuthPage) {
        return '/auth/login';
      }

      // Redirect to home if authenticated and on auth page
      if (isAuthenticated && isOnAuthPage) {
        return '/home';
      }

      return null;
    },
    routes: [
      // Splash
      GoRoute(
        path: '/splash',
        name: 'splash',
        builder: (context, state) => const SplashPage(),
      ),

      // Auth routes
      GoRoute(path: '/auth', redirect: (context, state) => '/auth/login'),
      GoRoute(
        path: '/auth/login',
        name: 'login',
        builder: (context, state) => const LoginPage(),
      ),
      GoRoute(
        path: '/auth/register',
        name: 'register',
        builder: (context, state) => const RegisterPage(),
      ),
      GoRoute(
        path: '/auth/forgot-password',
        name: 'forgot-password',
        builder: (context, state) => const ForgotPasswordPage(),
      ),

      // Main app routes
      GoRoute(
        path: '/home',
        name: 'home',
        builder: (context, state) => const HomePage(),
      ),

      // Trip routes
      GoRoute(
        path: '/trip/:tripId',
        name: 'trip-details',
        builder: (context, state) =>
            TripDetailsPage(tripId: state.pathParameters['tripId']!),
      ),
      GoRoute(
        path: '/create-trip',
        name: 'create-trip',
        builder: (context, state) => const CreateTripPage(),
      ),
      GoRoute(
        path: '/edit-trip/:tripId',
        name: 'edit-trip',
        builder: (context, state) =>
            EditTripPage(tripId: state.pathParameters['tripId']!),
      ),

      // Booking routes
      GoRoute(
        path: '/book/:tripId',
        name: 'book-trip',
        builder: (context, state) =>
            BookingPage(tripId: state.pathParameters['tripId']!),
      ),
      GoRoute(
        path: '/booking/:bookingId',
        name: 'booking-details',
        builder: (context, state) => BookingDetailsPage(
          bookingId: state.pathParameters['bookingId']!,
        ),
      ),

      // Profile routes
      GoRoute(
        path: '/profile',
        name: 'profile',
        builder: (context, state) => const ProfilePage(),
      ),
      GoRoute(
        path: '/profile/:userId',
        name: 'user-profile',
        builder: (context, state) =>
            ProfilePage(userId: state.pathParameters['userId']),
      ),
      GoRoute(
        path: '/edit-profile',
        name: 'edit-profile',
        builder: (context, state) => const EditProfilePage(),
      ),

      // Chat routes
      GoRoute(
        path: '/conversations',
        name: 'conversations',
        builder: (context, state) => const ConversationsPage(),
      ),
      GoRoute(
        path: '/chat/:conversationId',
        name: 'chat',
        builder: (context, state) => ChatPage(
          conversationId: state.pathParameters['conversationId']!,
        ),
      ),

      // Rating routes
      GoRoute(
        path: '/rate/:tripId/:userId',
        name: 'rate-user',
        builder: (context, state) => RatingPage(
          tripId: state.pathParameters['tripId']!,
          userId: state.pathParameters['userId']!,
        ),
      ),

      // Search routes
      GoRoute(
        path: '/search',
        name: 'search',
        builder: (context, state) => const SearchPage(),
      ),

      // Dashboard routes
      GoRoute(
        path: '/leader-dashboard',
        name: 'leader-dashboard',
        builder: (context, state) => const LeaderDashboardPage(),
      ),
      GoRoute(
        path: '/traveler-dashboard',
        name: 'traveler-dashboard',
        builder: (context, state) => const TravelerDashboardPage(),
      ),
    ],
    errorBuilder: (context, state) => Scaffold(
      appBar: AppBar(title: const Text('خطأ')),
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            const Text(
              'الصفحة غير موجودة',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 8),
            Text(
              'المسار: ${state.matchedLocation}',
              style: const TextStyle(color: Colors.grey),
            ),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => context.go('/home'),
              child: const Text('العودة للرئيسية'),
            ),
          ],
        ),
      ),
    ),
  );

  // Navigation helpers
  static void goToHome() {
    router.go('/home');
  }

  static void goToLogin() {
    router.go('/auth/login');
  }

  static void goToRegister() {
    router.go('/auth/register');
  }

  static void goToProfile([String? userId]) {
    if (userId != null) {
      router.go('/profile/$userId');
    } else {
      router.go('/profile');
    }
  }

  static void goToTripDetails(String tripId) {
    router.go('/trip/$tripId');
  }

  static void goToCreateTrip() {
    router.go('/create-trip');
  }

  static void goToBookTrip(String tripId) {
    router.go('/book/$tripId');
  }

  static void goToConversations() {
    router.go('/conversations');
  }

  static void goToChat(String conversationId) {
    router.go('/chat/$conversationId');
  }

  static void goToSearch() {
    router.go('/search');
  }

  static void goToLeaderDashboard() {
    router.go('/leader-dashboard');
  }

  static void goToTravelerDashboard() {
    router.go('/traveler-dashboard');
  }

  static void goBack() {
    if (router.canPop()) {
      router.pop();
    } else {
      goToHome();
    }
  }

  static void showSnackBar(String message, {bool isError = false}) {
    final context = navigatorKey.currentContext;
    if (context != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: isError ? Colors.red : null,
          behavior: SnackBarBehavior.floating,
        ),
      );
    }
  }

  static Future<T?> showBottomSheet<T>({
    required Widget child,
    bool isScrollControlled = true,
  }) {
    final context = navigatorKey.currentContext;
    if (context != null) {
      return showModalBottomSheet<T>(
        context: context,
        isScrollControlled: isScrollControlled,
        backgroundColor: Colors.transparent,
        builder: (context) => child,
      );
    }
    return Future.value(null);
  }

  static Future<T?> showCustomDialog<T>({
    required Widget child,
    bool barrierDismissible = true,
  }) {
    final context = navigatorKey.currentContext;
    if (context != null) {
      return showDialog<T>(
        context: context,
        barrierDismissible: barrierDismissible,
        builder: (context) => child,
      );
    }
    return Future.value(null);
  }
}
