import 'user_model.dart';

class TripModel {
  final String id;
  final String leaderId;
  final UserModel? leader;
  final String title;
  final String description;
  final String fromCity;
  final String toCity;
  final DateTime departureDate;
  final DateTime? returnDate;
  final String departureTime;
  final String? returnTime;
  final double price;
  final int totalSeats;
  final int availableSeats;
  final String tripType; // 'mixed', 'women_only', 'family_only'
  final String status; // 'active', 'completed', 'cancelled'
  final List<String> imageUrls;
  final String? carModel;
  final String? carColor;
  final String? carPlateNumber;
  final List<String> rules;
  final List<TripStop> stops;
  final Map<String, dynamic>? meetingPoint;
  final String? notes;
  final bool allowInstantBooking;
  final DateTime? bookingDeadline;
  final double rating;
  final int totalRatings;
  final List<String> amenities;
  final DateTime createdAt;
  final DateTime updatedAt;

  TripModel({
    required this.id,
    required this.leaderId,
    this.leader,
    required this.title,
    required this.description,
    required this.fromCity,
    required this.toCity,
    required this.departureDate,
    this.returnDate,
    required this.departureTime,
    this.returnTime,
    required this.price,
    required this.totalSeats,
    required this.availableSeats,
    this.tripType = 'mixed',
    this.status = 'active',
    this.imageUrls = const [],
    this.carModel,
    this.carColor,
    this.carPlateNumber,
    this.rules = const [],
    this.stops = const [],
    this.meetingPoint,
    this.notes,
    this.allowInstantBooking = false,
    this.bookingDeadline,
    this.rating = 0.0,
    this.totalRatings = 0,
    this.amenities = const [],
    required this.createdAt,
    required this.updatedAt,
  });

  factory TripModel.fromJson(Map<String, dynamic> json) {
    return TripModel(
      id: json['id'] as String,
      leaderId: json['leader_id'] as String,
      leader: json['leader'] != null 
          ? UserModel.fromJson(json['leader'] as Map<String, dynamic>)
          : null,
      title: json['title'] as String,
      description: json['description'] as String,
      fromCity: json['from_city'] as String,
      toCity: json['to_city'] as String,
      departureDate: DateTime.parse(json['departure_date'] as String),
      returnDate: json['return_date'] != null 
          ? DateTime.parse(json['return_date'] as String)
          : null,
      departureTime: json['departure_time'] as String,
      returnTime: json['return_time'] as String?,
      price: (json['price'] as num).toDouble(),
      totalSeats: json['total_seats'] as int,
      availableSeats: json['available_seats'] as int,
      tripType: json['trip_type'] as String? ?? 'mixed',
      status: json['status'] as String? ?? 'active',
      imageUrls: json['image_urls'] != null 
          ? List<String>.from(json['image_urls'] as List)
          : [],
      carModel: json['car_model'] as String?,
      carColor: json['car_color'] as String?,
      carPlateNumber: json['car_plate_number'] as String?,
      rules: json['rules'] != null 
          ? List<String>.from(json['rules'] as List)
          : [],
      stops: json['stops'] != null 
          ? (json['stops'] as List).map((stop) => TripStop.fromJson(stop)).toList()
          : [],
      meetingPoint: json['meeting_point'] as Map<String, dynamic>?,
      notes: json['notes'] as String?,
      allowInstantBooking: json['allow_instant_booking'] as bool? ?? false,
      bookingDeadline: json['booking_deadline'] != null 
          ? DateTime.parse(json['booking_deadline'] as String)
          : null,
      rating: (json['rating'] as num?)?.toDouble() ?? 0.0,
      totalRatings: json['total_ratings'] as int? ?? 0,
      amenities: json['amenities'] != null 
          ? List<String>.from(json['amenities'] as List)
          : [],
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'leader_id': leaderId,
      'title': title,
      'description': description,
      'from_city': fromCity,
      'to_city': toCity,
      'departure_date': departureDate.toIso8601String(),
      'return_date': returnDate?.toIso8601String(),
      'departure_time': departureTime,
      'return_time': returnTime,
      'price': price,
      'total_seats': totalSeats,
      'available_seats': availableSeats,
      'trip_type': tripType,
      'status': status,
      'image_urls': imageUrls,
      'car_model': carModel,
      'car_color': carColor,
      'car_plate_number': carPlateNumber,
      'rules': rules,
      'stops': stops.map((stop) => stop.toJson()).toList(),
      'meeting_point': meetingPoint,
      'notes': notes,
      'allow_instant_booking': allowInstantBooking,
      'booking_deadline': bookingDeadline?.toIso8601String(),
      'rating': rating,
      'total_ratings': totalRatings,
      'amenities': amenities,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  TripModel copyWith({
    String? id,
    String? leaderId,
    UserModel? leader,
    String? title,
    String? description,
    String? fromCity,
    String? toCity,
    DateTime? departureDate,
    DateTime? returnDate,
    String? departureTime,
    String? returnTime,
    double? price,
    int? totalSeats,
    int? availableSeats,
    String? tripType,
    String? status,
    List<String>? imageUrls,
    String? carModel,
    String? carColor,
    String? carPlateNumber,
    List<String>? rules,
    List<TripStop>? stops,
    Map<String, dynamic>? meetingPoint,
    String? notes,
    bool? allowInstantBooking,
    DateTime? bookingDeadline,
    double? rating,
    int? totalRatings,
    List<String>? amenities,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return TripModel(
      id: id ?? this.id,
      leaderId: leaderId ?? this.leaderId,
      leader: leader ?? this.leader,
      title: title ?? this.title,
      description: description ?? this.description,
      fromCity: fromCity ?? this.fromCity,
      toCity: toCity ?? this.toCity,
      departureDate: departureDate ?? this.departureDate,
      returnDate: returnDate ?? this.returnDate,
      departureTime: departureTime ?? this.departureTime,
      returnTime: returnTime ?? this.returnTime,
      price: price ?? this.price,
      totalSeats: totalSeats ?? this.totalSeats,
      availableSeats: availableSeats ?? this.availableSeats,
      tripType: tripType ?? this.tripType,
      status: status ?? this.status,
      imageUrls: imageUrls ?? this.imageUrls,
      carModel: carModel ?? this.carModel,
      carColor: carColor ?? this.carColor,
      carPlateNumber: carPlateNumber ?? this.carPlateNumber,
      rules: rules ?? this.rules,
      stops: stops ?? this.stops,
      meetingPoint: meetingPoint ?? this.meetingPoint,
      notes: notes ?? this.notes,
      allowInstantBooking: allowInstantBooking ?? this.allowInstantBooking,
      bookingDeadline: bookingDeadline ?? this.bookingDeadline,
      rating: rating ?? this.rating,
      totalRatings: totalRatings ?? this.totalRatings,
      amenities: amenities ?? this.amenities,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isActive => status == 'active';
  bool get isCompleted => status == 'completed';
  bool get isCancelled => status == 'cancelled';
  bool get hasAvailableSeats => availableSeats > 0;
  bool get isBookingOpen => bookingDeadline == null || DateTime.now().isBefore(bookingDeadline!);
  
  String get displayRating => rating > 0 ? rating.toStringAsFixed(1) : 'جديد';
  String get route => '$fromCity ← $toCity';
  
  int get bookedSeats => totalSeats - availableSeats;
}

class TripStop {
  final String name;
  final String? description;
  final DateTime estimatedTime;
  final int durationMinutes;
  final Map<String, dynamic>? location;

  TripStop({
    required this.name,
    this.description,
    required this.estimatedTime,
    this.durationMinutes = 15,
    this.location,
  });

  factory TripStop.fromJson(Map<String, dynamic> json) {
    return TripStop(
      name: json['name'] as String,
      description: json['description'] as String?,
      estimatedTime: DateTime.parse(json['estimated_time'] as String),
      durationMinutes: json['duration_minutes'] as int? ?? 15,
      location: json['location'] as Map<String, dynamic>?,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'description': description,
      'estimated_time': estimatedTime.toIso8601String(),
      'duration_minutes': durationMinutes,
      'location': location,
    };
  }
}
