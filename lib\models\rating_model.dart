import 'user_model.dart';
import 'trip_model.dart';

class RatingModel {
  final String id;
  final String tripId;
  final String raterId;
  final String ratedUserId;
  final TripModel? trip;
  final UserModel? rater;
  final UserModel? ratedUser;
  final double rating;
  final String? review;
  final List<String> tags; // e.g., ['punctual', 'friendly', 'clean_car']
  final bool isAnonymous;
  final DateTime createdAt;
  final DateTime updatedAt;

  RatingModel({
    required this.id,
    required this.tripId,
    required this.raterId,
    required this.ratedUserId,
    this.trip,
    this.rater,
    this.ratedUser,
    required this.rating,
    this.review,
    this.tags = const [],
    this.isAnonymous = false,
    required this.createdAt,
    required this.updatedAt,
  });

  factory RatingModel.fromJson(Map<String, dynamic> json) {
    return RatingModel(
      id: json['id'] as String,
      tripId: json['trip_id'] as String,
      raterId: json['rater_id'] as String,
      ratedUserId: json['rated_user_id'] as String,
      trip: json['trip'] != null 
          ? TripModel.fromJson(json['trip'] as Map<String, dynamic>)
          : null,
      rater: json['rater'] != null 
          ? UserModel.fromJson(json['rater'] as Map<String, dynamic>)
          : null,
      ratedUser: json['rated_user'] != null 
          ? UserModel.fromJson(json['rated_user'] as Map<String, dynamic>)
          : null,
      rating: (json['rating'] as num).toDouble(),
      review: json['review'] as String?,
      tags: json['tags'] != null 
          ? List<String>.from(json['tags'] as List)
          : [],
      isAnonymous: json['is_anonymous'] as bool? ?? false,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trip_id': tripId,
      'rater_id': raterId,
      'rated_user_id': ratedUserId,
      'rating': rating,
      'review': review,
      'tags': tags,
      'is_anonymous': isAnonymous,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  RatingModel copyWith({
    String? id,
    String? tripId,
    String? raterId,
    String? ratedUserId,
    TripModel? trip,
    UserModel? rater,
    UserModel? ratedUser,
    double? rating,
    String? review,
    List<String>? tags,
    bool? isAnonymous,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return RatingModel(
      id: id ?? this.id,
      tripId: tripId ?? this.tripId,
      raterId: raterId ?? this.raterId,
      ratedUserId: ratedUserId ?? this.ratedUserId,
      trip: trip ?? this.trip,
      rater: rater ?? this.rater,
      ratedUser: ratedUser ?? this.ratedUser,
      rating: rating ?? this.rating,
      review: review ?? this.review,
      tags: tags ?? this.tags,
      isAnonymous: isAnonymous ?? this.isAnonymous,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get displayRating => rating.toStringAsFixed(1);
  
  String get raterName => isAnonymous ? 'مستخدم مجهول' : (rater?.fullName ?? 'مستخدم');
  
  bool get hasReview => review != null && review!.isNotEmpty;
  bool get hasTags => tags.isNotEmpty;
  
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(createdAt);
    
    if (difference.inDays < 1) {
      return 'اليوم';
    } else if (difference.inDays < 7) {
      return 'منذ ${difference.inDays} يوم';
    } else if (difference.inDays < 30) {
      return 'منذ ${(difference.inDays / 7).floor()} أسبوع';
    } else if (difference.inDays < 365) {
      return 'منذ ${(difference.inDays / 30).floor()} شهر';
    } else {
      return 'منذ ${(difference.inDays / 365).floor()} سنة';
    }
  }
}

class RatingStats {
  final double averageRating;
  final int totalRatings;
  final Map<int, int> ratingDistribution; // star -> count
  final List<String> topTags;
  final int fiveStarCount;
  final int fourStarCount;
  final int threeStarCount;
  final int twoStarCount;
  final int oneStarCount;

  RatingStats({
    required this.averageRating,
    required this.totalRatings,
    required this.ratingDistribution,
    required this.topTags,
    required this.fiveStarCount,
    required this.fourStarCount,
    required this.threeStarCount,
    required this.twoStarCount,
    required this.oneStarCount,
  });

  factory RatingStats.fromJson(Map<String, dynamic> json) {
    return RatingStats(
      averageRating: (json['average_rating'] as num?)?.toDouble() ?? 0.0,
      totalRatings: json['total_ratings'] as int? ?? 0,
      ratingDistribution: Map<int, int>.from(json['rating_distribution'] ?? {}),
      topTags: List<String>.from(json['top_tags'] ?? []),
      fiveStarCount: json['five_star_count'] as int? ?? 0,
      fourStarCount: json['four_star_count'] as int? ?? 0,
      threeStarCount: json['three_star_count'] as int? ?? 0,
      twoStarCount: json['two_star_count'] as int? ?? 0,
      oneStarCount: json['one_star_count'] as int? ?? 0,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'average_rating': averageRating,
      'total_ratings': totalRatings,
      'rating_distribution': ratingDistribution,
      'top_tags': topTags,
      'five_star_count': fiveStarCount,
      'four_star_count': fourStarCount,
      'three_star_count': threeStarCount,
      'two_star_count': twoStarCount,
      'one_star_count': oneStarCount,
    };
  }

  String get displayRating => averageRating > 0 ? averageRating.toStringAsFixed(1) : 'جديد';
  
  double get fiveStarPercentage => totalRatings > 0 ? (fiveStarCount / totalRatings) * 100 : 0;
  double get fourStarPercentage => totalRatings > 0 ? (fourStarCount / totalRatings) * 100 : 0;
  double get threeStarPercentage => totalRatings > 0 ? (threeStarCount / totalRatings) * 100 : 0;
  double get twoStarPercentage => totalRatings > 0 ? (twoStarCount / totalRatings) * 100 : 0;
  double get oneStarPercentage => totalRatings > 0 ? (oneStarCount / totalRatings) * 100 : 0;
}
