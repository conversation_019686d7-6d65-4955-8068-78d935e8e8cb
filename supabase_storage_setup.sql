-- Supabase Storage Setup for <PERSON><PERSON><PERSON> App
-- Run these commands in your Supabase SQL editor to set up storage buckets

-- Create storage buckets
INSERT INTO storage.buckets (id, name, public, file_size_limit, allowed_mime_types)
VALUES 
  ('driver_documents', 'driver_documents', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp']),
  ('car_images', 'car_images', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp']),
  ('trip_gallery', 'trip_gallery', true, 10485760, ARRAY['image/jpeg', 'image/png', 'image/webp'])
ON CONFLICT (id) DO NOTHING;

-- Create RLS policies for driver_documents bucket
CREATE POLICY "Users can upload their own driver documents" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'driver_documents' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can view their own driver documents" ON storage.objects
  FOR SELECT USING (
    bucket_id = 'driver_documents' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can update their own driver documents" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'driver_documents' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own driver documents" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'driver_documents' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- Create RLS policies for car_images bucket
CREATE POLICY "Users can upload their own car images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'car_images' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Anyone can view car images" ON storage.objects
  FOR SELECT USING (bucket_id = 'car_images');

CREATE POLICY "Users can update their own car images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'car_images' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

CREATE POLICY "Users can delete their own car images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'car_images' 
    AND auth.uid()::text = (storage.foldername(name))[1]
  );

-- Create RLS policies for trip_gallery bucket
CREATE POLICY "Trip leaders can upload trip images" ON storage.objects
  FOR INSERT WITH CHECK (
    bucket_id = 'trip_gallery' 
    AND EXISTS (
      SELECT 1 FROM users 
      WHERE users.id = auth.uid() 
      AND users.is_leader = true
    )
  );

CREATE POLICY "Anyone can view trip gallery images" ON storage.objects
  FOR SELECT USING (bucket_id = 'trip_gallery');

CREATE POLICY "Trip owners can update their trip images" ON storage.objects
  FOR UPDATE USING (
    bucket_id = 'trip_gallery' 
    AND EXISTS (
      SELECT 1 FROM trips 
      WHERE trips.leader_id = auth.uid() 
      AND trips.id::text = (storage.foldername(name))[1]
    )
  );

CREATE POLICY "Trip owners can delete their trip images" ON storage.objects
  FOR DELETE USING (
    bucket_id = 'trip_gallery' 
    AND EXISTS (
      SELECT 1 FROM trips 
      WHERE trips.leader_id = auth.uid() 
      AND trips.id::text = (storage.foldername(name))[1]
    )
  );

-- Enable RLS on storage.objects if not already enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;

-- Grant necessary permissions
GRANT ALL ON storage.objects TO authenticated;
GRANT ALL ON storage.buckets TO authenticated;
