import 'user_model.dart';
import 'trip_model.dart';

class BookingModel {
  final String id;
  final String tripId;
  final String travelerId;
  final TripModel? trip;
  final UserModel? traveler;
  final int seatsBooked;
  final double totalPrice;
  final String status; // 'pending', 'confirmed', 'rejected', 'cancelled'
  final String? message;
  final String? rejectionReason;
  final DateTime? confirmedAt;
  final DateTime? cancelledAt;
  final Map<String, dynamic>? passengerDetails;
  final String? specialRequests;
  final bool isPaid;
  final String? paymentMethod;
  final String? paymentReference;
  final DateTime createdAt;
  final DateTime updatedAt;

  BookingModel({
    required this.id,
    required this.tripId,
    required this.travelerId,
    this.trip,
    this.traveler,
    required this.seatsBooked,
    required this.totalPrice,
    this.status = 'pending',
    this.message,
    this.rejectionReason,
    this.confirmedAt,
    this.cancelledAt,
    this.passengerDetails,
    this.specialRequests,
    this.isPaid = false,
    this.paymentMethod,
    this.paymentReference,
    required this.createdAt,
    required this.updatedAt,
  });

  factory BookingModel.fromJson(Map<String, dynamic> json) {
    return BookingModel(
      id: json['id'] as String,
      tripId: json['trip_id'] as String,
      travelerId: json['traveler_id'] as String,
      trip: json['trip'] != null 
          ? TripModel.fromJson(json['trip'] as Map<String, dynamic>)
          : null,
      traveler: json['traveler'] != null 
          ? UserModel.fromJson(json['traveler'] as Map<String, dynamic>)
          : null,
      seatsBooked: json['seats_booked'] as int,
      totalPrice: (json['total_price'] as num).toDouble(),
      status: json['status'] as String? ?? 'pending',
      message: json['message'] as String?,
      rejectionReason: json['rejection_reason'] as String?,
      confirmedAt: json['confirmed_at'] != null 
          ? DateTime.parse(json['confirmed_at'] as String)
          : null,
      cancelledAt: json['cancelled_at'] != null 
          ? DateTime.parse(json['cancelled_at'] as String)
          : null,
      passengerDetails: json['passenger_details'] as Map<String, dynamic>?,
      specialRequests: json['special_requests'] as String?,
      isPaid: json['is_paid'] as bool? ?? false,
      paymentMethod: json['payment_method'] as String?,
      paymentReference: json['payment_reference'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'trip_id': tripId,
      'traveler_id': travelerId,
      'seats_booked': seatsBooked,
      'total_price': totalPrice,
      'status': status,
      'message': message,
      'rejection_reason': rejectionReason,
      'confirmed_at': confirmedAt?.toIso8601String(),
      'cancelled_at': cancelledAt?.toIso8601String(),
      'passenger_details': passengerDetails,
      'special_requests': specialRequests,
      'is_paid': isPaid,
      'payment_method': paymentMethod,
      'payment_reference': paymentReference,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  BookingModel copyWith({
    String? id,
    String? tripId,
    String? travelerId,
    TripModel? trip,
    UserModel? traveler,
    int? seatsBooked,
    double? totalPrice,
    String? status,
    String? message,
    String? rejectionReason,
    DateTime? confirmedAt,
    DateTime? cancelledAt,
    Map<String, dynamic>? passengerDetails,
    String? specialRequests,
    bool? isPaid,
    String? paymentMethod,
    String? paymentReference,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return BookingModel(
      id: id ?? this.id,
      tripId: tripId ?? this.tripId,
      travelerId: travelerId ?? this.travelerId,
      trip: trip ?? this.trip,
      traveler: traveler ?? this.traveler,
      seatsBooked: seatsBooked ?? this.seatsBooked,
      totalPrice: totalPrice ?? this.totalPrice,
      status: status ?? this.status,
      message: message ?? this.message,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      confirmedAt: confirmedAt ?? this.confirmedAt,
      cancelledAt: cancelledAt ?? this.cancelledAt,
      passengerDetails: passengerDetails ?? this.passengerDetails,
      specialRequests: specialRequests ?? this.specialRequests,
      isPaid: isPaid ?? this.isPaid,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentReference: paymentReference ?? this.paymentReference,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isPending => status == 'pending';
  bool get isConfirmed => status == 'confirmed';
  bool get isRejected => status == 'rejected';
  bool get isCancelled => status == 'cancelled';
  
  String get statusText {
    switch (status) {
      case 'pending':
        return 'في الانتظار';
      case 'confirmed':
        return 'مؤكد';
      case 'rejected':
        return 'مرفوض';
      case 'cancelled':
        return 'ملغي';
      default:
        return status;
    }
  }
  
  String get pricePerSeat => (totalPrice / seatsBooked).toStringAsFixed(0);
}
