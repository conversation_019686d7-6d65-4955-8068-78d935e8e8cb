# سفرني | Safarni

**سافر جماعة، بتمن مناسب، بأمان تام!**

تطبيق مغربي لتنظيم الرحلات المشتركة بين المدن، يربط بين قادة الرحلات والمسافرين لتوفير تجربة سفر آمنة ومريحة واقتصادية.

## المميزات الرئيسية

### للمسافرين
- 🔍 **البحث المتقدم**: ابحث عن الرحلات حسب المدينة والتاريخ ونوع الرحلة
- 📱 **حجز سهل**: احجز مقعدك بضغطة واحدة
- 💬 **تواصل مباشر**: تحدث مع قائد الرحلة قبل وأثناء الرحلة
- ⭐ **نظام التقييم**: قيم تجربتك وساعد المجتمع
- 🔔 **إشعارات فورية**: تابع حالة حجزك والتحديثات

### لقادة الرحلات
- 🚗 **إنشاء الرحلات**: أنشئ رحلاتك بتفاصيل شاملة
- 👥 **إدارة الحجوزات**: اقبل أو ارفض طلبات الحجز
- 📊 **لوحة التحكم**: تابع إحصائياتك وأرباحك
- 🛡️ **ملف موثق**: احصل على شارة التوثيق لزيادة الثقة
- 💰 **أرباح مضمونة**: احصل على دخل إضافي من رحلاتك

## التقنيات المستخدمة

- **Flutter**: إطار العمل الأساسي للتطبيق
- **Supabase**: قاعدة البيانات والمصادقة والتخزين
- **Provider**: إدارة الحالة
- **Go Router**: التنقل بين الصفحات
- **Google Fonts**: خطوط عربية جميلة (Cairo)
- **Cached Network Image**: تحسين عرض الصور
- **Flutter Localizations**: دعم اللغة العربية والـ RTL

## البنية التقنية

```
lib/
├── constants/          # الثوابت والألوان والإعدادات
├── models/            # نماذج البيانات
├── services/          # خدمات التطبيق والـ API
├── providers/         # إدارة الحالة
├── pages/            # صفحات التطبيق
├── widgets/          # المكونات القابلة لإعادة الاستخدام
├── generated/        # ملفات الترجمة المولدة
└── l10n/            # ملفات الترجمة

assets/
├── images/           # الصور والأيقونات
├── fonts/           # الخطوط العربية
└── animations/      # الرسوم المتحركة
```

## إعداد المشروع

### المتطلبات
- Flutter SDK (3.13.0 أو أحدث)
- Dart SDK (3.1.0 أو أحدث)
- حساب Supabase

### خطوات التثبيت

1. **استنساخ المشروع**
```bash
git clone https://github.com/your-username/safarni.git
cd safarni
```

2. **تثبيت التبعيات**
```bash
flutter pub get
```

3. **إعداد Supabase**
   - أنشئ مشروع جديد في [Supabase](https://supabase.com)
   - انسخ URL و Anon Key
   - حدث الملف `lib/constants/app_constants.dart`:
```dart
static const String supabaseUrl = 'YOUR_SUPABASE_URL';
static const String supabaseAnonKey = 'YOUR_SUPABASE_ANON_KEY';
```

4. **إعداد قاعدة البيانات**
   - استخدم الـ SQL المرفق في `database/schema.sql` لإنشاء الجداول
   - فعل Row Level Security (RLS)
   - أنشئ Storage buckets للصور

5. **تشغيل التطبيق**
```bash
flutter run
```

## قاعدة البيانات

### الجداول الرئيسية

- **users**: بيانات المستخدمين
- **trips**: بيانات الرحلات
- **bookings**: الحجوزات
- **messages**: الرسائل
- **conversations**: المحادثات
- **ratings**: التقييمات

### Storage Buckets

- **profile-images**: صور الملفات الشخصية
- **trip-images**: صور الرحلات
- **car-images**: صور السيارات

## المميزات المطورة

### ✅ مكتمل
- [x] نظام المصادقة (تسجيل دخول/إنشاء حساب)
- [x] الواجهة الأساسية مع دعم RTL
- [x] نماذج البيانات
- [x] خدمات Supabase
- [x] إدارة الحالة بـ Provider
- [x] التنقل بـ Go Router
- [x] المكونات الأساسية (أزرار، حقول نص، بطاقات)
- [x] بيانات تجريبية للاختبار

### 🚧 قيد التطوير
- [ ] صفحات إنشاء وتعديل الرحلات
- [ ] نظام الحجز الكامل
- [ ] نظام المحادثات الفورية
- [ ] نظام التقييمات
- [ ] رفع وإدارة الصور
- [ ] الإشعارات Push
- [ ] نظام الدفع
- [ ] خرائط ومواقع GPS

### 🔮 مخطط مستقبلي
- [ ] تطبيق الويب
- [ ] لوحة تحكم الإدارة
- [ ] تحليلات متقدمة
- [ ] نظام النقاط والمكافآت
- [ ] دعم اللغة الفرنسية
- [ ] تكامل مع وسائل الدفع المحلية

## المساهمة

نرحب بمساهماتكم! يرجى:

1. Fork المشروع
2. إنشاء branch جديد للميزة
3. Commit التغييرات
4. Push إلى Branch
5. إنشاء Pull Request

## الترخيص

هذا المشروع مرخص تحت رخصة MIT - راجع ملف [LICENSE](LICENSE) للتفاصيل.

## التواصل

- **البريد الإلكتروني**: <EMAIL>
- **الموقع**: https://safarni.ma
- **تويتر**: @SafarniApp

---

**ملاحظة**: هذا المشروع في مرحلة التطوير. بعض المميزات قد تكون غير مكتملة أو تحتاج لتحسينات.
