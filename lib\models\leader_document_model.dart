class LeaderDocumentModel {
  final String id;
  final String userId;
  final String licenseImageUrl;
  final String? licenseNumber;
  final DateTime? licenseExpiryDate;
  final String verificationStatus; // 'pending', 'approved', 'rejected'
  final DateTime? verifiedAt;
  final String? verifiedBy;
  final DateTime createdAt;
  final DateTime updatedAt;

  LeaderDocumentModel({
    required this.id,
    required this.userId,
    required this.licenseImageUrl,
    this.licenseNumber,
    this.licenseExpiryDate,
    this.verificationStatus = 'pending',
    this.verifiedAt,
    this.verifiedBy,
    required this.createdAt,
    required this.updatedAt,
  });

  factory LeaderDocumentModel.fromJson(Map<String, dynamic> json) {
    return LeaderDocumentModel(
      id: json['id'] as String,
      userId: json['user_id'] as String,
      licenseImageUrl: json['license_image_url'] as String,
      licenseNumber: json['license_number'] as String?,
      licenseExpiryDate: json['license_expiry_date'] != null
          ? DateTime.parse(json['license_expiry_date'] as String)
          : null,
      verificationStatus: json['verification_status'] as String? ?? 'pending',
      verifiedAt: json['verified_at'] != null
          ? DateTime.parse(json['verified_at'] as String)
          : null,
      verifiedBy: json['verified_by'] as String?,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'user_id': userId,
      'license_image_url': licenseImageUrl,
      'license_number': licenseNumber,
      'license_expiry_date': licenseExpiryDate?.toIso8601String(),
      'verification_status': verificationStatus,
      'verified_at': verifiedAt?.toIso8601String(),
      'verified_by': verifiedBy,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  LeaderDocumentModel copyWith({
    String? id,
    String? userId,
    String? licenseImageUrl,
    String? licenseNumber,
    DateTime? licenseExpiryDate,
    String? verificationStatus,
    DateTime? verifiedAt,
    String? verifiedBy,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return LeaderDocumentModel(
      id: id ?? this.id,
      userId: userId ?? this.userId,
      licenseImageUrl: licenseImageUrl ?? this.licenseImageUrl,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      licenseExpiryDate: licenseExpiryDate ?? this.licenseExpiryDate,
      verificationStatus: verificationStatus ?? this.verificationStatus,
      verifiedAt: verifiedAt ?? this.verifiedAt,
      verifiedBy: verifiedBy ?? this.verifiedBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  bool get isPending => verificationStatus == 'pending';
  bool get isApproved => verificationStatus == 'approved';
  bool get isRejected => verificationStatus == 'rejected';

  String get statusDisplay {
    switch (verificationStatus) {
      case 'pending':
        return 'قيد المراجعة';
      case 'approved':
        return 'معتمد';
      case 'rejected':
        return 'مرفوض';
      default:
        return 'غير محدد';
    }
  }

  bool get isLicenseExpired {
    if (licenseExpiryDate == null) return false;
    return DateTime.now().isAfter(licenseExpiryDate!);
  }
}
