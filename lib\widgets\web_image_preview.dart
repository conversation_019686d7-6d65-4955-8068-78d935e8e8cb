import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:image_picker/image_picker.dart';

/// A widget that can display images from XFile for web compatibility
/// This replaces Image.file() which doesn't work on Flutter Web
class WebImagePreview extends StatefulWidget {
  final XFile? imageFile;
  final String? networkImageUrl;
  final double? width;
  final double? height;
  final BoxFit fit;
  final BorderRadius? borderRadius;
  final Widget? placeholder;
  final Widget? errorWidget;

  const WebImagePreview({
    super.key,
    this.imageFile,
    this.networkImageUrl,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.borderRadius,
    this.placeholder,
    this.errorWidget,
  });

  @override
  State<WebImagePreview> createState() => _WebImagePreviewState();
}

class _WebImagePreviewState extends State<WebImagePreview> {
  Uint8List? _imageBytes;
  bool _isLoading = false;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(WebImagePreview oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imageFile != widget.imageFile ||
        oldWidget.networkImageUrl != widget.networkImageUrl) {
      _loadImage();
    }
  }

  Future<void> _loadImage() async {
    if (widget.imageFile == null && widget.networkImageUrl == null) {
      setState(() {
        _imageBytes = null;
        _isLoading = false;
        _hasError = false;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      if (widget.imageFile != null) {
        // Load from XFile (works on both web and mobile)
        final bytes = await widget.imageFile!.readAsBytes();
        setState(() {
          _imageBytes = bytes;
          _isLoading = false;
        });
      } else {
        // For network images, we'll let Image.network handle it
        setState(() {
          _imageBytes = null;
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _hasError = true;
        _isLoading = false;
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget imageWidget;

    if (_isLoading) {
      imageWidget = widget.placeholder ??
          Container(
            width: widget.width,
            height: widget.height,
            color: Colors.grey[200],
            child: const Center(
              child: CircularProgressIndicator(),
            ),
          );
    } else if (_hasError) {
      imageWidget = widget.errorWidget ??
          Container(
            width: widget.width,
            height: widget.height,
            color: Colors.grey[200],
            child: const Center(
              child: Icon(
                Icons.error,
                color: Colors.red,
                size: 32,
              ),
            ),
          );
    } else if (_imageBytes != null) {
      // Display image from bytes (XFile)
      imageWidget = Image.memory(
        _imageBytes!,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
        errorBuilder: (context, error, stackTrace) {
          return widget.errorWidget ??
              Container(
                width: widget.width,
                height: widget.height,
                color: Colors.grey[200],
                child: const Center(
                  child: Icon(
                    Icons.broken_image,
                    color: Colors.grey,
                    size: 32,
                  ),
                ),
              );
        },
      );
    } else if (widget.networkImageUrl != null) {
      // Display network image
      imageWidget = Image.network(
        widget.networkImageUrl!,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
        loadingBuilder: (context, child, loadingProgress) {
          if (loadingProgress == null) return child;
          return widget.placeholder ??
              Container(
                width: widget.width,
                height: widget.height,
                color: Colors.grey[200],
                child: Center(
                  child: CircularProgressIndicator(
                    value: loadingProgress.expectedTotalBytes != null
                        ? loadingProgress.cumulativeBytesLoaded /
                            loadingProgress.expectedTotalBytes!
                        : null,
                  ),
                ),
              );
        },
        errorBuilder: (context, error, stackTrace) {
          return widget.errorWidget ??
              Container(
                width: widget.width,
                height: widget.height,
                color: Colors.grey[200],
                child: const Center(
                  child: Icon(
                    Icons.broken_image,
                    color: Colors.grey,
                    size: 32,
                  ),
                ),
              );
        },
      );
    } else {
      // No image to display
      imageWidget = widget.placeholder ??
          Container(
            width: widget.width,
            height: widget.height,
            color: Colors.grey[200],
            child: const Center(
              child: Icon(
                Icons.image,
                color: Colors.grey,
                size: 32,
              ),
            ),
          );
    }

    // Apply border radius if specified
    if (widget.borderRadius != null) {
      imageWidget = ClipRRect(
        borderRadius: widget.borderRadius!,
        child: imageWidget,
      );
    }

    return imageWidget;
  }
}
