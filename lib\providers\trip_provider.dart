import 'package:flutter/foundation.dart';
import '../models/trip_model.dart';
import '../models/booking_model.dart';
import '../services/mock_data_service.dart';
// import '../services/supabase_service.dart';

class TripProvider extends ChangeNotifier {
  List<TripModel> _trips = [];
  List<TripModel> _userTrips = [];
  List<BookingModel> _userBookings = [];
  TripModel? _selectedTrip;
  bool _isLoading = false;
  bool _isLoadingMore = false;
  String? _error;

  // Search and filter state
  String? _fromCity;
  String? _toCity;
  DateTime? _departureDate;
  String? _tripType;
  String _sortBy = 'departure_date';
  bool _sortAscending = true;

  // Pagination
  int _currentPage = 0;
  bool _hasMoreTrips = true;
  static const int _pageSize = 20;

  // Getters
  List<TripModel> get trips => _trips;
  List<TripModel> get userTrips => _userTrips;
  List<BookingModel> get userBookings => _userBookings;
  TripModel? get selectedTrip => _selectedTrip;
  bool get isLoading => _isLoading;
  bool get isLoadingMore => _isLoadingMore;
  String? get error => _error;
  bool get hasMoreTrips => _hasMoreTrips;

  // Search getters
  String? get fromCity => _fromCity;
  String? get toCity => _toCity;
  DateTime? get departureDate => _departureDate;
  String? get tripType => _tripType;
  String get sortBy => _sortBy;
  bool get sortAscending => _sortAscending;

  // Load trips with filters
  Future<void> loadTrips({bool refresh = false}) async {
    if (refresh) {
      _currentPage = 0;
      _hasMoreTrips = true;
      _trips.clear();
    }

    if (!_hasMoreTrips || _isLoading) return;

    try {
      _setLoading(refresh);
      _clearError();

      await Future.delayed(
          const Duration(seconds: 1)); // Simulate network delay
      final allTrips = MockDataService.mockTrips;
      final newTrips =
          allTrips.skip(_currentPage * _pageSize).take(_pageSize).toList();

      if (refresh) {
        _trips = newTrips;
      } else {
        _trips.addAll(newTrips);
      }

      _hasMoreTrips = newTrips.length == _pageSize;
      _currentPage++;
    } catch (e) {
      _setError('فشل في تحميل الرحلات');
    } finally {
      _setLoading(false);
    }
  }

  // Load more trips (pagination)
  Future<void> loadMoreTrips() async {
    if (_isLoadingMore || !_hasMoreTrips) return;

    try {
      _isLoadingMore = true;
      notifyListeners();

      await Future.delayed(const Duration(seconds: 1));
      final allTrips = MockDataService.mockTrips;
      final newTrips =
          allTrips.skip(_currentPage * _pageSize).take(_pageSize).toList();

      _trips.addAll(newTrips);
      _hasMoreTrips = newTrips.length == _pageSize;
      _currentPage++;
    } catch (e) {
      _setError('فشل في تحميل المزيد من الرحلات');
    } finally {
      _isLoadingMore = false;
      notifyListeners();
    }
  }

  // Load trip by ID
  Future<void> loadTripById(String tripId) async {
    try {
      _setLoading(true);
      _clearError();

      await Future.delayed(const Duration(milliseconds: 500));
      _selectedTrip = MockDataService.getTripById(tripId);
    } catch (e) {
      _setError('فشل في تحميل تفاصيل الرحلة');
    } finally {
      _setLoading(false);
    }
  }

  // Load user trips
  Future<void> loadUserTrips(String userId) async {
    try {
      _setLoading(true);
      _clearError();

      await Future.delayed(const Duration(milliseconds: 500));
      _userTrips = MockDataService.getTripsByLeader(userId);
    } catch (e) {
      _setError('فشل في تحميل رحلاتك');
    } finally {
      _setLoading(false);
    }
  }

  // Load user bookings
  Future<void> loadUserBookings(String userId) async {
    try {
      _setLoading(true);
      _clearError();

      await Future.delayed(const Duration(milliseconds: 500));
      _userBookings = MockDataService.getBookingsByTraveler(userId);
    } catch (e) {
      _setError('فشل في تحميل حجوزاتك');
    } finally {
      _setLoading(false);
    }
  }

  // Create trip
  Future<bool> createTrip(TripModel trip) async {
    try {
      _setLoading(true);
      _clearError();

      await Future.delayed(const Duration(milliseconds: 500));
      final tripId = 'trip_${DateTime.now().millisecondsSinceEpoch}';

      // Add to user trips
      final newTrip = trip.copyWith(id: tripId);
      _userTrips.insert(0, newTrip);

      return true;
    } catch (e) {
      _setError('فشل في إنشاء الرحلة');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update trip
  Future<bool> updateTrip(TripModel trip) async {
    try {
      _setLoading(true);
      _clearError();

      await Future.delayed(const Duration(milliseconds: 500));

      // Update in lists
      _updateTripInLists(trip);

      return true;
    } catch (e) {
      _setError('فشل في تحديث الرحلة');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Create booking
  Future<bool> createBooking(BookingModel booking) async {
    try {
      _setLoading(true);
      _clearError();

      await Future.delayed(const Duration(milliseconds: 500));
      final bookingId = 'booking_${DateTime.now().millisecondsSinceEpoch}';

      // Add to user bookings
      final newBooking = booking.copyWith(id: bookingId);
      _userBookings.insert(0, newBooking);

      return true;
    } catch (e) {
      _setError('فشل في إنشاء الحجز');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Update booking
  Future<bool> updateBooking(BookingModel booking) async {
    try {
      _setLoading(true);
      _clearError();

      await Future.delayed(const Duration(milliseconds: 500));

      // Update in user bookings
      final index = _userBookings.indexWhere((b) => b.id == booking.id);
      if (index != -1) {
        _userBookings[index] = booking;
      }

      return true;
    } catch (e) {
      _setError('فشل في تحديث الحجز');
      return false;
    } finally {
      _setLoading(false);
    }
  }

  // Search and filter methods
  void setFromCity(String? city) {
    _fromCity = city;
    notifyListeners();
  }

  void setToCity(String? city) {
    _toCity = city;
    notifyListeners();
  }

  void setDepartureDate(DateTime? date) {
    _departureDate = date;
    notifyListeners();
  }

  void setTripType(String? type) {
    _tripType = type;
    notifyListeners();
  }

  void setSorting(String sortBy, bool ascending) {
    _sortBy = sortBy;
    _sortAscending = ascending;
    _sortTrips();
    notifyListeners();
  }

  void clearFilters() {
    _fromCity = null;
    _toCity = null;
    _departureDate = null;
    _tripType = null;
    notifyListeners();
  }

  // Apply search filters
  Future<void> applyFilters() async {
    await loadTrips(refresh: true);
  }

  // Sort trips
  void _sortTrips() {
    _trips.sort((a, b) {
      int comparison = 0;

      switch (_sortBy) {
        case 'price':
          comparison = a.price.compareTo(b.price);
          break;
        case 'departure_date':
          comparison = a.departureDate.compareTo(b.departureDate);
          break;
        case 'rating':
          comparison = a.rating.compareTo(b.rating);
          break;
        case 'available_seats':
          comparison = a.availableSeats.compareTo(b.availableSeats);
          break;
        default:
          comparison = a.departureDate.compareTo(b.departureDate);
      }

      return _sortAscending ? comparison : -comparison;
    });
  }

  // Helper methods
  void _updateTripInLists(TripModel trip) {
    // Update in trips list
    final tripIndex = _trips.indexWhere((t) => t.id == trip.id);
    if (tripIndex != -1) {
      _trips[tripIndex] = trip;
    }

    // Update in user trips list
    final userTripIndex = _userTrips.indexWhere((t) => t.id == trip.id);
    if (userTripIndex != -1) {
      _userTrips[userTripIndex] = trip;
    }

    // Update selected trip
    if (_selectedTrip?.id == trip.id) {
      _selectedTrip = trip;
    }
  }

  void _setLoading(bool loading) {
    _isLoading = loading;
    notifyListeners();
  }

  void _setError(String error) {
    _error = error;
    notifyListeners();
  }

  void _clearError() {
    _error = null;
    notifyListeners();
  }

  void clearError() {
    _clearError();
  }

  void clearSelectedTrip() {
    _selectedTrip = null;
    notifyListeners();
  }

  // Get filtered trips count
  int get filteredTripsCount => _trips.length;

  // Check if filters are active
  bool get hasActiveFilters =>
      _fromCity != null ||
      _toCity != null ||
      _departureDate != null ||
      (_tripType != null && _tripType != 'all');
}
