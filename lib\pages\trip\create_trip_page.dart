import 'package:flutter/material.dart';
import '../../constants/app_theme.dart';
import '../../generated/l10n.dart';

class CreateTripPage extends StatelessWidget {
  const CreateTripPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(S.of(context).createTrip),
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
      ),
      body: const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.add_circle, size: 64, color: AppColors.primary),
            SizedBox(height: 16),
            Text('إنشاء رحلة جديدة'),
            SizedBox(height: 8),
            Text('هذه الصفحة قيد التطوير'),
          ],
        ),
      ),
    );
  }
}

// Placeholder pages for navigation
class EditTripPage extends StatelessWidget {
  final String tripId;
  const EditTripPage({super.key, required this.tripId});
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('تعديل الرحلة')),
    body: Center(child: Text('تعديل الرحلة: $tripId')),
  );
}

class BookingPage extends StatelessWidget {
  final String tripId;
  const BookingPage({super.key, required this.tripId});
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('حجز الرحلة')),
    body: Center(child: Text('حجز الرحلة: $tripId')),
  );
}

class BookingDetailsPage extends StatelessWidget {
  final String bookingId;
  const BookingDetailsPage({super.key, required this.bookingId});
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('تفاصيل الحجز')),
    body: Center(child: Text('تفاصيل الحجز: $bookingId')),
  );
}

class ProfilePage extends StatelessWidget {
  final String? userId;
  const ProfilePage({super.key, this.userId});
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('الملف الشخصي')),
    body: Center(child: Text('الملف الشخصي: ${userId ?? 'الحالي'}')),
  );
}

class EditProfilePage extends StatelessWidget {
  const EditProfilePage({super.key});
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('تعديل الملف الشخصي')),
    body: const Center(child: Text('تعديل الملف الشخصي')),
  );
}

class ConversationsPage extends StatelessWidget {
  const ConversationsPage({super.key});
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('المحادثات')),
    body: const Center(child: Text('المحادثات')),
  );
}

class ChatPage extends StatelessWidget {
  final String conversationId;
  const ChatPage({super.key, required this.conversationId});
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('المحادثة')),
    body: Center(child: Text('المحادثة: $conversationId')),
  );
}

class RatingPage extends StatelessWidget {
  final String tripId;
  final String userId;
  const RatingPage({super.key, required this.tripId, required this.userId});
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('التقييم')),
    body: Center(child: Text('تقييم المستخدم: $userId للرحلة: $tripId')),
  );
}

class SearchPage extends StatelessWidget {
  const SearchPage({super.key});
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('البحث')),
    body: const Center(child: Text('البحث عن الرحلات')),
  );
}

class LeaderDashboardPage extends StatelessWidget {
  const LeaderDashboardPage({super.key});
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('لوحة قائد الرحلة')),
    body: const Center(child: Text('لوحة قائد الرحلة')),
  );
}

class TravelerDashboardPage extends StatelessWidget {
  const TravelerDashboardPage({super.key});
  @override
  Widget build(BuildContext context) => Scaffold(
    appBar: AppBar(title: const Text('لوحة المسافر')),
    body: const Center(child: Text('لوحة المسافر')),
  );
}
