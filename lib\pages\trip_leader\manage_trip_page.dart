import 'package:flutter/material.dart';
import 'package:provider/provider.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../constants/app_theme.dart';
import '../../models/trip_model.dart';
import '../../models/booking_model.dart';
import '../../providers/trip_provider.dart';
import '../../utils/navigation_utils.dart';

class ManageTripPage extends StatefulWidget {
  final TripModel trip;

  const ManageTripPage({super.key, required this.trip});

  @override
  State<ManageTripPage> createState() => _ManageTripPageState();
}

class _ManageTripPageState extends State<ManageTripPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 2, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final bookedSeats = widget.trip.totalSeats - widget.trip.availableSeats;
    final progressValue = bookedSeats / widget.trip.totalSeats;

    return Scaffold(
      backgroundColor: AppColors.background,
      body: NestedScrollView(
        headerSliverBuilder: (context, innerBoxIsScrolled) {
          return [
            SliverAppBar(
              expandedHeight: 300,
              floating: false,
              pinned: true,
              backgroundColor: AppColors.primary,
              flexibleSpace: FlexibleSpaceBar(
                title: Text(
                  widget.trip.title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                background: Stack(
                  fit: StackFit.expand,
                  children: [
                    // Trip Image
                    widget.trip.imageUrls.isNotEmpty
                        ? CachedNetworkImage(
                            imageUrl: widget.trip.imageUrls.first,
                            fit: BoxFit.cover,
                            placeholder: (context, url) => Container(
                              color: AppColors.surfaceVariant,
                              child: const Center(
                                child: CircularProgressIndicator(),
                              ),
                            ),
                            errorWidget: (context, url, error) => Container(
                              color: AppColors.surfaceVariant,
                              child: const Icon(
                                Icons.directions_car,
                                size: 64,
                                color: AppColors.textTertiary,
                              ),
                            ),
                          )
                        : Container(
                            color: AppColors.surfaceVariant,
                            child: const Icon(
                              Icons.directions_car,
                              size: 64,
                              color: AppColors.textTertiary,
                            ),
                          ),

                    // Gradient Overlay
                    Container(
                      decoration: BoxDecoration(
                        gradient: LinearGradient(
                          begin: Alignment.topCenter,
                          end: Alignment.bottomCenter,
                          colors: [
                            Colors.transparent,
                            Colors.black.withOpacity(0.7),
                          ],
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              actions: [
                PopupMenuButton<String>(
                  icon: const Icon(Icons.more_vert, color: Colors.white),
                  onSelected: (value) {
                    switch (value) {
                      case 'edit':
                        _editTrip();
                        break;
                      case 'share':
                        _shareTrip();
                        break;
                      case 'cancel':
                        _showCancelDialog();
                        break;
                    }
                  },
                  itemBuilder: (context) => [
                    const PopupMenuItem(
                      value: 'edit',
                      child: Row(
                        children: [
                          Icon(Icons.edit),
                          SizedBox(width: 8),
                          Text('تعديل الرحلة'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'share',
                      child: Row(
                        children: [
                          Icon(Icons.share),
                          SizedBox(width: 8),
                          Text('مشاركة الرحلة'),
                        ],
                      ),
                    ),
                    const PopupMenuItem(
                      value: 'cancel',
                      child: Row(
                        children: [
                          Icon(Icons.cancel, color: Colors.red),
                          SizedBox(width: 8),
                          Text('إلغاء الرحلة',
                              style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ];
        },
        body: Column(
          children: [
            // Trip Stats
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: Column(
                children: [
                  // Route Info
                  Row(
                    children: [
                      Icon(Icons.location_on, color: AppColors.primary),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          '${widget.trip.fromCity} ← ${widget.trip.toCity}',
                          style: theme.textTheme.titleLarge?.copyWith(
                            fontWeight: FontWeight.bold,
                            color: AppColors.primary,
                          ),
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Stats Row
                  Row(
                    children: [
                      Expanded(
                        child: _buildStatCard(
                          'المقاعد المحجوزة',
                          '$bookedSeats / ${widget.trip.totalSeats}',
                          Icons.airline_seat_recline_normal,
                          AppColors.primary,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Expanded(
                        child: _buildStatCard(
                          'الأرباح المتوقعة',
                          '${(bookedSeats * widget.trip.price).toInt()} درهم',
                          Icons.attach_money,
                          AppColors.secondary,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 16),

                  // Progress Bar
                  Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'نسبة الإشغال',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              color: AppColors.textSecondary,
                            ),
                          ),
                          Text(
                            '${(progressValue * 100).round()}%',
                            style: theme.textTheme.bodyMedium?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: AppColors.textPrimary,
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      LinearProgressIndicator(
                        value: progressValue,
                        backgroundColor: AppColors.surfaceVariant,
                        valueColor: AlwaysStoppedAnimation<Color>(
                          progressValue >= 0.8
                              ? Colors.green
                              : progressValue >= 0.5
                                  ? Colors.orange
                                  : AppColors.primary,
                        ),
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Tab Bar
            Container(
              decoration: BoxDecoration(
                color: Colors.white,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 4,
                    offset: const Offset(0, 2),
                  ),
                ],
              ),
              child: TabBar(
                controller: _tabController,
                labelColor: AppColors.primary,
                unselectedLabelColor: AppColors.textSecondary,
                indicatorColor: AppColors.primary,
                tabs: const [
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.people),
                        SizedBox(width: 8),
                        Text('الركاب'),
                      ],
                    ),
                  ),
                  Tab(
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(Icons.info),
                        SizedBox(width: 8),
                        Text('تفاصيل الرحلة'),
                      ],
                    ),
                  ),
                ],
              ),
            ),

            // Tab Views
            Expanded(
              child: TabBarView(
                controller: _tabController,
                children: [
                  _PassengersTab(trip: widget.trip),
                  _TripDetailsTab(trip: widget.trip),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatCard(
      String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: AppColors.textSecondary,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _editTrip() {
    // TODO: Navigate to edit trip page
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('تعديل الرحلة - قيد التطوير')),
    );
  }

  void _shareTrip() {
    // TODO: Implement share functionality
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('مشاركة الرحلة - قيد التطوير')),
    );
  }

  void _showCancelDialog() async {
    final confirmed = await NavigationUtils.showConfirmationDialog(
      context,
      title: 'إلغاء الرحلة',
      content:
          'هل أنت متأكد من إلغاء هذه الرحلة؟ لا يمكن التراجع عن هذا الإجراء.',
      confirmText: 'إلغاء الرحلة',
      cancelText: 'إلغاء',
      confirmColor: Colors.red,
    );

    if (confirmed == true) {
      _cancelTrip();
    }
  }

  void _cancelTrip() {
    // TODO: Implement cancel trip logic with Supabase
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إلغاء الرحلة'),
        backgroundColor: Colors.red,
      ),
    );
    Navigator.pop(context);
  }
}

// Passengers Tab
class _PassengersTab extends StatelessWidget {
  final TripModel trip;

  const _PassengersTab({required this.trip});

  @override
  Widget build(BuildContext context) {
    // TODO: Load actual passengers from Supabase
    final passengers = <BookingModel>[]; // Mock empty list

    if (passengers.isEmpty) {
      return Center(
        child: Padding(
          padding: const EdgeInsets.all(32),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.people_outline,
                size: 80,
                color: AppColors.textTertiary,
              ),
              const SizedBox(height: 24),
              Text(
                'لا يوجد ركاب بعد',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      color: AppColors.textSecondary,
                      fontWeight: FontWeight.bold,
                    ),
                textAlign: TextAlign.center,
              ),
              const SizedBox(height: 8),
              Text(
                'ستظهر قائمة الركاب المؤكدين هنا',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: AppColors.textTertiary,
                    ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      );
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: passengers.length,
      itemBuilder: (context, index) {
        return _PassengerCard(booking: passengers[index]);
      },
    );
  }
}

// Trip Details Tab
class _TripDetailsTab extends StatelessWidget {
  final TripModel trip;

  const _TripDetailsTab({required this.trip});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Basic Info Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'معلومات أساسية',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  _buildDetailRow('التاريخ', _formatDate(trip.departureDate)),
                  _buildDetailRow('الوقت', trip.departureTime),
                  _buildDetailRow('السعر', '${trip.price.toInt()} درهم'),
                  _buildDetailRow(
                      'نوع الرحلة', _getTripTypeText(trip.tripType)),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Vehicle Info Card
          Card(
            child: Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'معلومات المركبة',
                    style: theme.textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 16),
                  if (trip.carModel != null)
                    _buildDetailRow('نوع المركبة', trip.carModel!),
                  if (trip.carColor != null)
                    _buildDetailRow('اللون', trip.carColor!),
                  if (trip.carPlateNumber != null)
                    _buildDetailRow('رقم اللوحة', trip.carPlateNumber!),
                ],
              ),
            ),
          ),
          const SizedBox(height: 16),

          // Rules Card
          if (trip.rules.isNotEmpty)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'قوانين الرحلة',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    ...trip.rules.map((rule) => Padding(
                          padding: const EdgeInsets.only(bottom: 8),
                          child: Row(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              Icon(
                                Icons.check_circle,
                                size: 16,
                                color: AppColors.primary,
                              ),
                              const SizedBox(width: 8),
                              Expanded(
                                child: Text(
                                  rule,
                                  style: theme.textTheme.bodyMedium,
                                ),
                              ),
                            ],
                          ),
                        )),
                  ],
                ),
              ),
            ),
          const SizedBox(height: 16),

          // Amenities Card
          if (trip.amenities.isNotEmpty)
            Card(
              child: Padding(
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'المرافق المتوفرة',
                      style: theme.textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    const SizedBox(height: 16),
                    Wrap(
                      spacing: 8,
                      runSpacing: 8,
                      children: trip.amenities
                          .map((amenity) => Chip(
                                label: Text(_getAmenityText(amenity)),
                                backgroundColor:
                                    AppColors.primary.withOpacity(0.1),
                                labelStyle: TextStyle(color: AppColors.primary),
                              ))
                          .toList(),
                    ),
                  ],
                ),
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildDetailRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          SizedBox(
            width: 100,
            child: Text(
              label,
              style: TextStyle(
                color: AppColors.textSecondary,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    final months = [
      'يناير',
      'فبراير',
      'مارس',
      'أبريل',
      'مايو',
      'يونيو',
      'يوليو',
      'أغسطس',
      'سبتمبر',
      'أكتوبر',
      'نوفمبر',
      'ديسمبر'
    ];
    return '${date.day} ${months[date.month - 1]} ${date.year}';
  }

  String _getTripTypeText(String tripType) {
    switch (tripType) {
      case 'mixed':
        return 'رحلة مختلطة';
      case 'women_only':
        return 'رحلة نسائية فقط';
      case 'family_only':
        return 'رحلة عائلية';
      default:
        return 'غير محدد';
    }
  }

  String _getAmenityText(String amenity) {
    switch (amenity) {
      case 'wifi':
        return 'واي فاي';
      case 'ac':
        return 'تكييف';
      case 'music':
        return 'موسيقى';
      case 'charging':
        return 'شحن الهاتف';
      case 'water':
        return 'مياه';
      case 'snacks':
        return 'وجبات خفيفة';
      default:
        return amenity;
    }
  }
}

// Passenger Card
class _PassengerCard extends StatelessWidget {
  final BookingModel booking;

  const _PassengerCard({required this.booking});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);

    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Row(
          children: [
            // User Photo
            Container(
              width: 50,
              height: 50,
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: AppColors.border),
                image: booking.traveler?.profileImageUrl != null
                    ? DecorationImage(
                        image: NetworkImage(booking.traveler!.profileImageUrl!),
                        fit: BoxFit.cover,
                      )
                    : null,
              ),
              child: booking.traveler?.profileImageUrl == null
                  ? Icon(
                      Icons.person,
                      size: 24,
                      color: AppColors.textSecondary,
                    )
                  : null,
            ),
            const SizedBox(width: 12),

            // User Details
            Expanded(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    booking.traveler?.fullName ?? 'مسافر',
                    style: theme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const SizedBox(height: 4),
                  Row(
                    children: [
                      Icon(
                        Icons.airline_seat_recline_normal,
                        size: 16,
                        color: AppColors.textSecondary,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        '${booking.seatsBooked} مقعد',
                        style: theme.textTheme.bodyMedium?.copyWith(
                          color: AppColors.textSecondary,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
            ),

            // Actions
            Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                IconButton(
                  icon: const Icon(Icons.message),
                  onPressed: () {
                    // TODO: Navigate to chat
                    ScaffoldMessenger.of(context).showSnackBar(
                      const SnackBar(content: Text('المحادثة - قيد التطوير')),
                    );
                  },
                ),
                IconButton(
                  icon: const Icon(Icons.remove_circle, color: Colors.red),
                  onPressed: () {
                    _showRemovePassengerDialog(context, booking);
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  void _showRemovePassengerDialog(BuildContext context, BookingModel booking) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('إزالة الراكب'),
        content: Text(
            'هل أنت متأكد من إزالة ${booking.traveler?.fullName ?? 'هذا الراكب'} من الرحلة؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () {
              Navigator.pop(context);
              _removePassenger(context, booking);
            },
            child: const Text('إزالة', style: TextStyle(color: Colors.red)),
          ),
        ],
      ),
    );
  }

  void _removePassenger(BuildContext context, BookingModel booking) {
    // TODO: Implement remove passenger logic with Supabase
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(
        content: Text('تم إزالة الراكب'),
        backgroundColor: Colors.red,
      ),
    );
  }
}
