import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:image_picker/image_picker.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:uuid/uuid.dart';

class StorageService {
  static final SupabaseClient _supabase = Supabase.instance.client;
  static const String _driverDocumentsBucket = 'driver_documents';
  static const String _carImagesBucket = 'car_images';
  static const String _tripGalleryBucket = 'trip_gallery';

  /// Upload an image to Supabase Storage and return the public URL
  /// This method works for both web and mobile platforms
  static Future<String?> uploadImage({
    required XFile imageFile,
    required String bucket,
    String? folder,
    String? customFileName,
  }) async {
    try {
      // Generate unique filename
      final uuid = const Uuid();
      final fileExtension = imageFile.name.split('.').last.toLowerCase();
      final fileName = customFileName ?? '${uuid.v4()}.$fileExtension';
      final fullPath = folder != null ? '$folder/$fileName' : fileName;

      // Read image bytes (works for both web and mobile)
      final Uint8List imageBytes = await imageFile.readAsBytes();

      // Upload to Supabase Storage
      final response = await _supabase.storage
          .from(bucket)
          .uploadBinary(fullPath, imageBytes);

      if (response.isNotEmpty) {
        // Get public URL
        final publicUrl = _supabase.storage
            .from(bucket)
            .getPublicUrl(fullPath);
        
        return publicUrl;
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
      }
      return null;
    }
  }

  /// Upload driver license image
  static Future<String?> uploadDriverLicense({
    required XFile imageFile,
    required String userId,
  }) async {
    return await uploadImage(
      imageFile: imageFile,
      bucket: _driverDocumentsBucket,
      folder: 'licenses',
      customFileName: 'license_$userId.${imageFile.name.split('.').last}',
    );
  }

  /// Upload car image
  static Future<String?> uploadCarImage({
    required XFile imageFile,
    required String userId,
  }) async {
    return await uploadImage(
      imageFile: imageFile,
      bucket: _carImagesBucket,
      folder: 'vehicles',
      customFileName: 'vehicle_$userId.${imageFile.name.split('.').last}',
    );
  }

  /// Upload trip gallery image
  static Future<String?> uploadTripImage({
    required XFile imageFile,
    required String tripId,
    int? imageIndex,
  }) async {
    final fileName = imageIndex != null 
        ? 'trip_${tripId}_$imageIndex.${imageFile.name.split('.').last}'
        : null;
    
    return await uploadImage(
      imageFile: imageFile,
      bucket: _tripGalleryBucket,
      folder: 'trips',
      customFileName: fileName,
    );
  }

  /// Delete an image from storage
  static Future<bool> deleteImage({
    required String bucket,
    required String filePath,
  }) async {
    try {
      await _supabase.storage
          .from(bucket)
          .remove([filePath]);
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting image: $e');
      }
      return false;
    }
  }

  /// Get file path from public URL
  static String? getFilePathFromUrl(String publicUrl, String bucket) {
    try {
      final uri = Uri.parse(publicUrl);
      final pathSegments = uri.pathSegments;
      final bucketIndex = pathSegments.indexOf(bucket);
      
      if (bucketIndex != -1 && bucketIndex < pathSegments.length - 1) {
        return pathSegments.sublist(bucketIndex + 1).join('/');
      }
      
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing URL: $e');
      }
      return null;
    }
  }

  /// Check if storage buckets exist and are accessible
  static Future<bool> checkStorageHealth() async {
    try {
      // Try to list files in each bucket to verify access
      await _supabase.storage.from(_driverDocumentsBucket).list();
      await _supabase.storage.from(_carImagesBucket).list();
      await _supabase.storage.from(_tripGalleryBucket).list();
      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Storage health check failed: $e');
      }
      return false;
    }
  }
}
