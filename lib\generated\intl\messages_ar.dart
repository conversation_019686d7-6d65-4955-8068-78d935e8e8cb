// DO NOT EDIT. This is code generated via package:intl/generate_localized.dart
// This is a library that provides messages for the ar locale. All the
// messages from the main program should be duplicated here with the same
// function name.

// Ignore issues from commonly used lints in this file.
// ignore_for_file:unnecessary_brace_in_string_interps, unnecessary_new
// ignore_for_file:prefer_single_quotes,comment_references, directives_ordering
// ignore_for_file:annotate_overrides,prefer_generic_function_type_aliases
// ignore_for_file:unused_import, file_names, avoid_escaping_inner_quotes
// ignore_for_file:unnecessary_string_interpolations, unnecessary_string_escapes

import 'package:intl/intl.dart';
import 'package:intl/message_lookup_by_library.dart';

final messages = new MessageLookup();

typedef String MessageIfAbsent(String messageStr, List<dynamic> args);

class MessageLookup extends MessageLookupByLibrary {
  String get localeName => 'ar';

  final messages = _notInlinedMessages(_notInlinedMessages);
  static Map<String, Function> _notInlinedMessages(_) => <String, Function>{
        "appName": MessageLookupByLibrary.simpleMessage("سفرني"),
        "appSlogan": MessageLookupByLibrary.simpleMessage("سافر جماعة، بتمن مناسب، بأمان تام!"),
        "welcome": MessageLookupByLibrary.simpleMessage("مرحباً"),
        "login": MessageLookupByLibrary.simpleMessage("تسجيل الدخول"),
        "register": MessageLookupByLibrary.simpleMessage("إنشاء حساب"),
        "logout": MessageLookupByLibrary.simpleMessage("تسجيل الخروج"),
        "forgotPassword": MessageLookupByLibrary.simpleMessage("نسيت كلمة المرور؟"),
        "resetPassword": MessageLookupByLibrary.simpleMessage("إعادة تعيين كلمة المرور"),
        "email": MessageLookupByLibrary.simpleMessage("البريد الإلكتروني"),
        "password": MessageLookupByLibrary.simpleMessage("كلمة المرور"),
        "confirmPassword": MessageLookupByLibrary.simpleMessage("تأكيد كلمة المرور"),
        "fullName": MessageLookupByLibrary.simpleMessage("الاسم الكامل"),
        "phone": MessageLookupByLibrary.simpleMessage("رقم الهاتف"),
        "city": MessageLookupByLibrary.simpleMessage("المدينة"),
        "dateOfBirth": MessageLookupByLibrary.simpleMessage("تاريخ الميلاد"),
        "gender": MessageLookupByLibrary.simpleMessage("الجنس"),
        "male": MessageLookupByLibrary.simpleMessage("ذكر"),
        "female": MessageLookupByLibrary.simpleMessage("أنثى"),
        "bio": MessageLookupByLibrary.simpleMessage("نبذة شخصية"),
        "tripLeader": MessageLookupByLibrary.simpleMessage("قائد الرحلة"),
        "traveler": MessageLookupByLibrary.simpleMessage("مسافر"),
        "selectRole": MessageLookupByLibrary.simpleMessage("اختر نوع الحساب"),
        "home": MessageLookupByLibrary.simpleMessage("الرئيسية"),
        "search": MessageLookupByLibrary.simpleMessage("البحث"),
        "myTrips": MessageLookupByLibrary.simpleMessage("رحلاتي"),
        "bookings": MessageLookupByLibrary.simpleMessage("حجوزاتي"),
        "profile": MessageLookupByLibrary.simpleMessage("الملف الشخصي"),
        "messages": MessageLookupByLibrary.simpleMessage("الرسائل"),
        "notifications": MessageLookupByLibrary.simpleMessage("الإشعارات"),
        "createTrip": MessageLookupByLibrary.simpleMessage("إنشاء رحلة"),
        "editTrip": MessageLookupByLibrary.simpleMessage("تعديل الرحلة"),
        "tripDetails": MessageLookupByLibrary.simpleMessage("تفاصيل الرحلة"),
        "bookTrip": MessageLookupByLibrary.simpleMessage("احجز الرحلة"),
        "cancelBooking": MessageLookupByLibrary.simpleMessage("إلغاء الحجز"),
        "from": MessageLookupByLibrary.simpleMessage("من"),
        "to": MessageLookupByLibrary.simpleMessage("إلى"),
        "departureDate": MessageLookupByLibrary.simpleMessage("تاريخ المغادرة"),
        "returnDate": MessageLookupByLibrary.simpleMessage("تاريخ العودة"),
        "departureTime": MessageLookupByLibrary.simpleMessage("وقت المغادرة"),
        "returnTime": MessageLookupByLibrary.simpleMessage("وقت العودة"),
        "price": MessageLookupByLibrary.simpleMessage("السعر"),
        "pricePerSeat": MessageLookupByLibrary.simpleMessage("السعر للمقعد"),
        "totalSeats": MessageLookupByLibrary.simpleMessage("إجمالي المقاعد"),
        "availableSeats": MessageLookupByLibrary.simpleMessage("المقاعد المتاحة"),
        "bookedSeats": MessageLookupByLibrary.simpleMessage("المقاعد المحجوزة"),
        "tripType": MessageLookupByLibrary.simpleMessage("نوع الرحلة"),
        "mixed": MessageLookupByLibrary.simpleMessage("مختلط"),
        "womenOnly": MessageLookupByLibrary.simpleMessage("نساء فقط"),
        "familyOnly": MessageLookupByLibrary.simpleMessage("عائلات فقط"),
        "loading": MessageLookupByLibrary.simpleMessage("جاري التحميل..."),
        "error": MessageLookupByLibrary.simpleMessage("خطأ"),
        "success": MessageLookupByLibrary.simpleMessage("نجح"),
        "noData": MessageLookupByLibrary.simpleMessage("لا توجد بيانات"),
        "tryAgain": MessageLookupByLibrary.simpleMessage("حاول مرة أخرى"),
      };
}
