import 'package:flutter/material.dart';
import '../constants/app_theme.dart';

class RatingStars extends StatelessWidget {
  final double rating;
  final double size;
  final Color? color;
  final Color? unratedColor;
  final bool allowHalfRating;

  const RatingStars({
    super.key,
    required this.rating,
    this.size = 16,
    this.color,
    this.unratedColor,
    this.allowHalfRating = true,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return Icon(
          _getStarIcon(index + 1, rating),
          size: size,
          color: _getStarColor(index + 1, rating),
        );
      }),
    );
  }

  IconData _getStarIcon(int position, double rating) {
    if (rating >= position) {
      return Icons.star;
    } else if (allowHalfRating && rating >= position - 0.5) {
      return Icons.star_half;
    } else {
      return Icons.star_border;
    }
  }

  Color _getStarColor(int position, double rating) {
    if (rating >= position || (allowHalfRating && rating >= position - 0.5)) {
      return color ?? Colors.amber;
    } else {
      return unratedColor ?? AppColors.textTertiary;
    }
  }
}

class InteractiveRatingStars extends StatefulWidget {
  final double initialRating;
  final double size;
  final Color? color;
  final Color? unratedColor;
  final void Function(double)? onRatingChanged;
  final bool allowHalfRating;

  const InteractiveRatingStars({
    super.key,
    this.initialRating = 0,
    this.size = 24,
    this.color,
    this.unratedColor,
    this.onRatingChanged,
    this.allowHalfRating = false,
  });

  @override
  State<InteractiveRatingStars> createState() => _InteractiveRatingStarsState();
}

class _InteractiveRatingStarsState extends State<InteractiveRatingStars> {
  late double _currentRating;

  @override
  void initState() {
    super.initState();
    _currentRating = widget.initialRating;
  }

  @override
  Widget build(BuildContext context) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: List.generate(5, (index) {
        return GestureDetector(
          onTap: () {
            setState(() {
              _currentRating = (index + 1).toDouble();
            });
            widget.onRatingChanged?.call(_currentRating);
          },
          child: Icon(
            _getStarIcon(index + 1, _currentRating),
            size: widget.size,
            color: _getStarColor(index + 1, _currentRating),
          ),
        );
      }),
    );
  }

  IconData _getStarIcon(int position, double rating) {
    if (rating >= position) {
      return Icons.star;
    } else if (widget.allowHalfRating && rating >= position - 0.5) {
      return Icons.star_half;
    } else {
      return Icons.star_border;
    }
  }

  Color _getStarColor(int position, double rating) {
    if (rating >= position || (widget.allowHalfRating && rating >= position - 0.5)) {
      return widget.color ?? Colors.amber;
    } else {
      return widget.unratedColor ?? AppColors.textTertiary;
    }
  }
}

class RatingDisplay extends StatelessWidget {
  final double rating;
  final int totalRatings;
  final double starSize;
  final TextStyle? textStyle;
  final bool showCount;

  const RatingDisplay({
    super.key,
    required this.rating,
    this.totalRatings = 0,
    this.starSize = 16,
    this.textStyle,
    this.showCount = true,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        RatingStars(
          rating: rating,
          size: starSize,
        ),
        const SizedBox(width: 4),
        Text(
          rating > 0 ? rating.toStringAsFixed(1) : 'جديد',
          style: textStyle ?? theme.textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
            fontWeight: FontWeight.w600,
          ),
        ),
        if (showCount && totalRatings > 0) ...[
          Text(
            ' ($totalRatings)',
            style: textStyle ?? theme.textTheme.bodySmall?.copyWith(
              color: AppColors.textTertiary,
            ),
          ),
        ],
      ],
    );
  }
}

class RatingBar extends StatelessWidget {
  final int starCount;
  final int totalRatings;
  final double percentage;
  final Color? barColor;

  const RatingBar({
    super.key,
    required this.starCount,
    required this.totalRatings,
    required this.percentage,
    this.barColor,
  });

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    
    return Row(
      children: [
        Text(
          '$starCount',
          style: theme.textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
        const SizedBox(width: 4),
        const Icon(
          Icons.star,
          size: 12,
          color: Colors.amber,
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Container(
            height: 8,
            decoration: BoxDecoration(
              color: AppColors.borderLight,
              borderRadius: BorderRadius.circular(4),
            ),
            child: FractionallySizedBox(
              alignment: Alignment.centerRight,
              widthFactor: percentage / 100,
              child: Container(
                decoration: BoxDecoration(
                  color: barColor ?? Colors.amber,
                  borderRadius: BorderRadius.circular(4),
                ),
              ),
            ),
          ),
        ),
        const SizedBox(width: 8),
        Text(
          '$totalRatings',
          style: theme.textTheme.bodySmall?.copyWith(
            color: AppColors.textSecondary,
          ),
        ),
      ],
    );
  }
}
