// GENERATED CODE - DO NOT MODIFY BY HAND
import 'package:flutter/material.dart';
import 'package:intl/intl.dart';
import 'intl/messages_all.dart';

class S {
  S();

  static S? _current;

  static S get current {
    assert(_current != null,
        'No instance of S was loaded. Try to initialize the S delegate before accessing S.current.');
    return _current!;
  }

  static const AppLocalizationDelegate delegate = AppLocalizationDelegate();

  static Future<S> load(Locale locale) {
    final name = (locale.countryCode?.isEmpty ?? false)
        ? locale.languageCode
        : locale.toString();
    final localeName = Intl.canonicalizedLocale(name);
    return initializeMessages(localeName).then((_) {
      Intl.defaultLocale = localeName;
      final instance = S();
      S._current = instance;

      return instance;
    });
  }

  static S of(BuildContext context) {
    final instance = S.maybeOf(context);
    assert(instance != null,
        'No instance of S present in the widget tree. Did you add S.delegate in localizationsDelegates?');
    return instance!;
  }

  static S? maybeOf(BuildContext context) {
    return Localizations.of(context, S);
  }

  String get appName => Intl.message('سفرني', name: 'appName');
  String get appSlogan => Intl.message('سافر جماعة، بتمن مناسب، بأمان تام!', name: 'appSlogan');
  String get welcome => Intl.message('مرحباً', name: 'welcome');
  String get login => Intl.message('تسجيل الدخول', name: 'login');
  String get register => Intl.message('إنشاء حساب', name: 'register');
  String get logout => Intl.message('تسجيل الخروج', name: 'logout');
  String get forgotPassword => Intl.message('نسيت كلمة المرور؟', name: 'forgotPassword');
  String get resetPassword => Intl.message('إعادة تعيين كلمة المرور', name: 'resetPassword');
  String get email => Intl.message('البريد الإلكتروني', name: 'email');
  String get password => Intl.message('كلمة المرور', name: 'password');
  String get confirmPassword => Intl.message('تأكيد كلمة المرور', name: 'confirmPassword');
  String get fullName => Intl.message('الاسم الكامل', name: 'fullName');
  String get phone => Intl.message('رقم الهاتف', name: 'phone');
  String get city => Intl.message('المدينة', name: 'city');
  String get dateOfBirth => Intl.message('تاريخ الميلاد', name: 'dateOfBirth');
  String get gender => Intl.message('الجنس', name: 'gender');
  String get male => Intl.message('ذكر', name: 'male');
  String get female => Intl.message('أنثى', name: 'female');
  String get bio => Intl.message('نبذة شخصية', name: 'bio');
  String get tripLeader => Intl.message('قائد الرحلة', name: 'tripLeader');
  String get traveler => Intl.message('مسافر', name: 'traveler');
  String get selectRole => Intl.message('اختر نوع الحساب', name: 'selectRole');
  String get home => Intl.message('الرئيسية', name: 'home');
  String get search => Intl.message('البحث', name: 'search');
  String get myTrips => Intl.message('رحلاتي', name: 'myTrips');
  String get bookings => Intl.message('حجوزاتي', name: 'bookings');
  String get profile => Intl.message('الملف الشخصي', name: 'profile');
  String get messages => Intl.message('الرسائل', name: 'messages');
  String get notifications => Intl.message('الإشعارات', name: 'notifications');
  String get createTrip => Intl.message('إنشاء رحلة', name: 'createTrip');
  String get editTrip => Intl.message('تعديل الرحلة', name: 'editTrip');
  String get tripDetails => Intl.message('تفاصيل الرحلة', name: 'tripDetails');
  String get bookTrip => Intl.message('احجز الرحلة', name: 'bookTrip');
  String get cancelBooking => Intl.message('إلغاء الحجز', name: 'cancelBooking');
  String get from => Intl.message('من', name: 'from');
  String get to => Intl.message('إلى', name: 'to');
  String get departureDate => Intl.message('تاريخ المغادرة', name: 'departureDate');
  String get returnDate => Intl.message('تاريخ العودة', name: 'returnDate');
  String get departureTime => Intl.message('وقت المغادرة', name: 'departureTime');
  String get returnTime => Intl.message('وقت العودة', name: 'returnTime');
  String get price => Intl.message('السعر', name: 'price');
  String get pricePerSeat => Intl.message('السعر للمقعد', name: 'pricePerSeat');
  String get totalSeats => Intl.message('إجمالي المقاعد', name: 'totalSeats');
  String get availableSeats => Intl.message('المقاعد المتاحة', name: 'availableSeats');
  String get bookedSeats => Intl.message('المقاعد المحجوزة', name: 'bookedSeats');
  String get tripType => Intl.message('نوع الرحلة', name: 'tripType');
  String get mixed => Intl.message('مختلط', name: 'mixed');
  String get womenOnly => Intl.message('نساء فقط', name: 'womenOnly');
  String get familyOnly => Intl.message('عائلات فقط', name: 'familyOnly');
  String get carDetails => Intl.message('تفاصيل السيارة', name: 'carDetails');
  String get carModel => Intl.message('موديل السيارة', name: 'carModel');
  String get carColor => Intl.message('لون السيارة', name: 'carColor');
  String get carPlateNumber => Intl.message('رقم اللوحة', name: 'carPlateNumber');
  String get meetingPoint => Intl.message('نقطة التجمع', name: 'meetingPoint');
  String get rules => Intl.message('قوانين الرحلة', name: 'rules');
  String get notes => Intl.message('ملاحظات', name: 'notes');
  String get amenities => Intl.message('المرافق', name: 'amenities');
  String get rating => Intl.message('التقييم', name: 'rating');
  String get reviews => Intl.message('المراجعات', name: 'reviews');
  String get rateTrip => Intl.message('قيم الرحلة', name: 'rateTrip');
  String get rateUser => Intl.message('قيم المستخدم', name: 'rateUser');
  String get writeReview => Intl.message('اكتب مراجعة', name: 'writeReview');
  String get pending => Intl.message('في الانتظار', name: 'pending');
  String get confirmed => Intl.message('مؤكد', name: 'confirmed');
  String get rejected => Intl.message('مرفوض', name: 'rejected');
  String get cancelled => Intl.message('ملغي', name: 'cancelled');
  String get completed => Intl.message('مكتمل', name: 'completed');
  String get active => Intl.message('نشط', name: 'active');
  String get accept => Intl.message('قبول', name: 'accept');
  String get reject => Intl.message('رفض', name: 'reject');
  String get cancel => Intl.message('إلغاء', name: 'cancel');
  String get save => Intl.message('حفظ', name: 'save');
  String get edit => Intl.message('تعديل', name: 'edit');
  String get delete => Intl.message('حذف', name: 'delete');
  String get share => Intl.message('مشاركة', name: 'share');
  String get report => Intl.message('إبلاغ', name: 'report');
  String get loading => Intl.message('جاري التحميل...', name: 'loading');
  String get error => Intl.message('خطأ', name: 'error');
  String get success => Intl.message('نجح', name: 'success');
  String get noData => Intl.message('لا توجد بيانات', name: 'noData');
  String get tryAgain => Intl.message('حاول مرة أخرى', name: 'tryAgain');
}

class AppLocalizationDelegate extends LocalizationsDelegate<S> {
  const AppLocalizationDelegate();

  List<Locale> get supportedLocales {
    return const <Locale>[
      Locale.fromSubtags(languageCode: 'ar'),
    ];
  }

  @override
  bool isSupported(Locale locale) => _isSupported(locale);
  @override
  Future<S> load(Locale locale) => S.load(locale);
  @override
  bool shouldReload(AppLocalizationDelegate old) => false;

  bool _isSupported(Locale locale) {
    for (var supportedLocale in supportedLocales) {
      if (supportedLocale.languageCode == locale.languageCode) {
        return true;
      }
    }
    return false;
  }
}
