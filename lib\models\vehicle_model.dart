class VehicleModel {
  final String id;
  final String ownerId;
  final String make;
  final String model;
  final int? year;
  final String color;
  final String plateNumber;
  final String? imageUrl;
  final int seats;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;

  VehicleModel({
    required this.id,
    required this.ownerId,
    required this.make,
    required this.model,
    this.year,
    required this.color,
    required this.plateNumber,
    this.imageUrl,
    this.seats = 4,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
  });

  factory VehicleModel.fromJson(Map<String, dynamic> json) {
    return VehicleModel(
      id: json['id'] as String,
      ownerId: json['owner_id'] as String,
      make: json['make'] as String,
      model: json['model'] as String,
      year: json['year'] as int?,
      color: json['color'] as String,
      plateNumber: json['plate_number'] as String,
      imageUrl: json['image_url'] as String?,
      seats: json['seats'] as int? ?? 4,
      isActive: json['is_active'] as bool? ?? true,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: DateTime.parse(json['updated_at'] as String),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'owner_id': ownerId,
      'make': make,
      'model': model,
      'year': year,
      'color': color,
      'plate_number': plateNumber,
      'image_url': imageUrl,
      'seats': seats,
      'is_active': isActive,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  VehicleModel copyWith({
    String? id,
    String? ownerId,
    String? make,
    String? model,
    int? year,
    String? color,
    String? plateNumber,
    String? imageUrl,
    int? seats,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return VehicleModel(
      id: id ?? this.id,
      ownerId: ownerId ?? this.ownerId,
      make: make ?? this.make,
      model: model ?? this.model,
      year: year ?? this.year,
      color: color ?? this.color,
      plateNumber: plateNumber ?? this.plateNumber,
      imageUrl: imageUrl ?? this.imageUrl,
      seats: seats ?? this.seats,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  String get displayName => '$make $model';
  String get displayInfo => '$displayName - $color - $plateNumber';
  String get yearDisplay => year != null ? year.toString() : 'غير محدد';
}
