import 'package:flutter/foundation.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import '../models/user_model.dart';
import '../services/supabase_service.dart';

class WalletService {
  // Commission rates
  static const double tripCommissionRate = 0.05; // 5%
  static const double leaderActivationFee = 20.0; // 20 MAD
  static const double minimumBalanceForTrips = 5.0; // 5 MAD

  /// Charge wallet with specified amount
  static Future<bool> chargeWallet({
    required String userId,
    required double amount,
    String? paymentMethod,
    String? transactionId,
  }) async {
    try {
      // Get current user
      final user = await SupabaseService.getUserProfile(userId);
      if (user == null) return false;

      // Update balance
      final newBalance = user.balance + amount;
      final updatedUser = user.copyWith(balance: newBalance);

      await SupabaseService.updateUserProfile(updatedUser);

      // Record transaction
      await _recordTransaction(
        userId: userId,
        type: 'deposit',
        amount: amount,
        description: 'شحن المحفظة',
      );

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Charge wallet error: $e');
      }
      return false;
    }
  }

  /// Deduct commission from trip creation
  static Future<bool> deductTripCommission({
    required String userId,
    required double tripPrice,
    required String tripId,
  }) async {
    try {
      // Get current user
      final user = await SupabaseService.getUserProfile(userId);
      if (user == null) return false;

      // Calculate commission
      final commissionAmount = tripPrice * tripCommissionRate;

      // Check if user has sufficient balance
      if (user.balance < commissionAmount) {
        return false;
      }

      // Update balance
      final newBalance = user.balance - commissionAmount;
      final updatedUser = user.copyWith(balance: newBalance);

      await SupabaseService.updateUserProfile(updatedUser);

      // Record transaction
      await _recordTransaction(
        userId: userId,
        type: 'commission',
        amount: -commissionAmount,
        description: 'عمولة إنشاء رحلة',
        tripId: tripId,
      );

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Deduct commission error: $e');
      }
      return false;
    }
  }

  /// Deduct leader activation fee
  static Future<bool> deductActivationFee({
    required String userId,
  }) async {
    try {
      // Get current user
      final user = await SupabaseService.getUserProfile(userId);
      if (user == null) return false;

      // Check if user has sufficient balance
      if (user.balance < leaderActivationFee) {
        return false;
      }

      // Update balance
      final newBalance = user.balance - leaderActivationFee;
      final updatedUser = user.copyWith(
        balance: newBalance,
        isLeader: true,
        role: 'trip_leader',
      );

      await SupabaseService.updateUserProfile(updatedUser);

      // Record transaction
      await _recordTransaction(
        userId: userId,
        type: 'withdrawal',
        amount: -leaderActivationFee,
        description: 'رسوم تفعيل وضع القائد',
      );

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Deduct activation fee error: $e');
      }
      return false;
    }
  }

  /// Add earnings from completed trip
  static Future<bool> addTripEarnings({
    required String userId,
    required double amount,
    required String tripId,
  }) async {
    try {
      // Get current user
      final user = await SupabaseService.getUserProfile(userId);
      if (user == null) return false;

      // Update balance
      final newBalance = user.balance + amount;
      final updatedUser = user.copyWith(balance: newBalance);

      await SupabaseService.updateUserProfile(updatedUser);

      // Record transaction
      await _recordTransaction(
        userId: userId,
        type: 'earning',
        amount: amount,
        description: 'أرباح من رحلة مكتملة',
        tripId: tripId,
      );

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Add trip earnings error: $e');
      }
      return false;
    }
  }

  /// Get transaction history
  static Future<List<Map<String, dynamic>>> getTransactionHistory({
    required String userId,
    int limit = 50,
    int offset = 0,
  }) async {
    try {
      final response = await Supabase.instance.client
          .from('transactions')
          .select()
          .eq('user_id', userId)
          .order('created_at', ascending: false)
          .range(offset, offset + limit - 1);

      return List<Map<String, dynamic>>.from(response);
    } catch (e) {
      if (kDebugMode) {
        print('Get transaction history error: $e');
      }
      return [];
    }
  }

  /// Record a transaction
  static Future<void> _recordTransaction({
    required String userId,
    required String type,
    required double amount,
    required String description,
    String? tripId,
  }) async {
    try {
      await Supabase.instance.client.from('transactions').insert({
        'user_id': userId,
        'transaction_type': type,
        'amount': amount,
        'description': description,
        'trip_id': tripId,
        'status': 'completed',
        'created_at': DateTime.now().toIso8601String(),
      });
    } catch (e) {
      if (kDebugMode) {
        print('Record transaction error: $e');
      }
      // Don't throw error for transaction recording failures
    }
  }

  /// Check if user can create trips
  static bool canCreateTrips(UserModel user) {
    return user.isLeader && user.balance >= minimumBalanceForTrips;
  }

  /// Calculate commission amount
  static double calculateCommission(double tripPrice) {
    return tripPrice * tripCommissionRate;
  }

  /// Format amount for display
  static String formatAmount(double amount) {
    return '${amount.toStringAsFixed(2)} درهم';
  }
}
